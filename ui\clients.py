from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QSizePolicy, QFrame, QMenu, QAction,
                            QTableWidget, QTableWidgetItem, QHeaderView, QDialog,
                            QFormLayout, QTextEdit, QDoubleSpinBox, QMessageBox, QShortcut)
from PyQt5.QtCore import Qt, QTimer, QSize
from PyQt5.QtGui import (QFont, QColor, QPainter, QIcon, QPixmap, QBrush, QKeySequence,
                        QRadialGradient, QPen)

from database import Client
from utils import format_currency
from ui.unified_styles import UnifiedStyles

class ClientsWidget(QWidget):
    """واجهة إدارة العملاء"""

    def __init__(self, session):
        super().__init__()
        try:
            self.session = session
            self.init_ui()
        except Exception as e:
            print(f"خطأ في تهيئة قسم العملاء: {str(e)}")
            # إنشاء واجهة بسيطة في حالة الخطأ
            from PyQt5.QtWidgets import QVBoxLayout, QLabel
            layout = QVBoxLayout()
            error_label = QLabel(f"خطأ في تحميل قسم العملاء: {str(e)}")
            layout.addWidget(error_label)
            self.setLayout(layout)

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للفواتير
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("🤝 إدارة العملاء المتطورة - نظام شامل ومتقدم لإدارة العملاء مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للفواتير
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(3)  # مسافات أقل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(4, 0, 4, 0)  # هوامش جانبية أقل
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث باسم العميل، الهاتف، البريد الإلكتروني أو العنوان...")
        self.search_edit.textChanged.connect(self.filter_clients)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        search_button.clicked.connect(self.filter_clients)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة
        self.create_custom_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول العملاء المتطور والمحسن
        self.create_advanced_clients_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.clients_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للفواتير
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(4)

        # زر الإضافة
        self.add_button = QPushButton("➕ إضافة عميل")
        self.style_advanced_button(self.add_button, 'emerald')
        self.add_button.clicked.connect(self.add_client)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')
        self.edit_button.clicked.connect(self.edit_client)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر الحذف
        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')
        self.delete_button.clicked.connect(self.delete_client)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التحديث
        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر عرض التفاصيل
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'cyan')
        self.view_button.clicked.connect(self.view_client)
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التصدير
        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'orange', has_menu=True)
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة التصدير
        export_menu = QMenu(self)
        export_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(234, 88, 12, 0.8);
                border-radius: 15px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 200px;
            }
            QMenu::item {
                background: transparent;
                padding: 12px 20px;
                margin: 2px;
                border-radius: 8px;
                color: #1f2937;
                font-weight: 700;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(234, 88, 12, 0.2),
                    stop:0.5 rgba(249, 115, 22, 0.15),
                    stop:1 rgba(251, 146, 60, 0.2));
                color: #1e40af;
                border: 2px solid rgba(234, 88, 12, 0.4);
                font-weight: 900;
            }
        """)

        excel_action = QAction("📊 تصدير Excel", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        self.export_button.setMenu(export_menu)

        # زر الإحصائيات
        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.statistics_button)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        main_layout.addWidget(bottom_frame)

        self.setLayout(main_layout)

        # تهيئة حالة الأزرار (الأزرار الأساسية مفعلة، أزرار التحديد معطلة)
        self.initialize_button_states()

        # تأجيل تحميل البيانات لتحسين الأداء
        QTimer.singleShot(50, self.refresh_data)

        # إنشاء بيانات تجريبية إذا لم توجد
        QTimer.singleShot(75, self.create_sample_data_if_empty)

    def initialize_button_states(self):
        """تهيئة حالة الأزرار عند البداية - جميع الأزرار منيرة ومفعلة"""
        try:
            print("🔧 بدء تهيئة حالة الأزرار...")

            # تفعيل جميع الأزرار وجعلها منيرة
            buttons = [
                (self.add_button, "➕ إضافة عميل"),
                (self.edit_button, "✏️ تعديل"),
                (self.delete_button, "🗑️ حذف"),
                (self.refresh_button, "🔄 تحديث"),
                (self.view_button, "👁️ عرض التفاصيل"),
                (self.export_button, "📤 تصدير"),
                (self.statistics_button, "📊 الإحصائيات")
            ]

            for button, name in buttons:
                # تفعيل الزر
                button.setEnabled(True)

                # إزالة أي تأثيرات شفافية وجعل الزر منير
                current_style = button.styleSheet()
                # إزالة أي opacity موجودة
                import re
                clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                # إضافة opacity كاملة لجعل الزر منير
                bright_style = clean_style + "\nQPushButton { opacity: 1.0 !important; }"
                button.setStyleSheet(bright_style)
                button.show()

                pass  # تم تنوير الزر بنجاح

            pass  # تم تهيئة جميع الأزرار بنجاح

        except Exception as e:
            # في حالة الخطأ، تفعيل جميع الأزرار بالطريقة التقليدية
            try:
                self.add_button.setEnabled(True)
                self.edit_button.setEnabled(True)
                self.delete_button.setEnabled(True)
                self.refresh_button.setEnabled(True)
                self.view_button.setEnabled(True)
                self.export_button.setEnabled(True)
                self.statistics_button.setEnabled(True)
            except Exception as e2:
                pass  # خطأ في التفعيل التقليدي

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة بدون مشاكل"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QFrame:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QFrame:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
        """)

        # تخطيط أفقي للإطار
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(0, 0, 0, 0)
        filter_layout.setSpacing(0)

        # سهم يسار (مشابه للسهم الأيمن)
        self.left_arrow = QPushButton("▼")
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 25px;
                max-height: 25px;
                padding: 0px;
                margin: 0px;
                text-align: center;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                transition: all 0.2s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px);
                box-shadow: 0 4px 10px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # النص الحالي
        self.current_filter_label = QLabel("جميع الحالات")
        self.current_filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص أفقياً وعمودياً
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.filter_menu_button = QPushButton("▼")
        self.filter_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 25px;
                max-height: 25px;
                padding: 0px;
                margin: 0px;
                text-align: center;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                transition: all 0.2s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px);
                box-shadow: 0 4px 10px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # إضافة العناصر للتخطيط - سهم يسار، النص في المنتصف، سهم يمين
        filter_layout.addWidget(self.left_arrow, 0)
        filter_layout.addWidget(self.current_filter_label, 1)
        filter_layout.addWidget(self.filter_menu_button, 0)

        self.status_filter_frame.setLayout(filter_layout)

        # إنشاء القائمة المنسدلة المخصصة
        self.create_filter_menu()

        # ربط الأزرار بالقائمة
        self.filter_menu_button.clicked.connect(self.show_filter_menu)
        self.left_arrow.clicked.connect(self.show_filter_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
        self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

        # جعل الإطار قابل للتركيز للحصول على تأثيرات أفضل
        self.status_filter_frame.setFocusPolicy(Qt.ClickFocus)

        # تعيين القيمة الافتراضية
        self.current_filter_value = None

        # استخدام الإطار كـ status_filter
        self.status_filter = self.status_filter_frame

    def create_filter_menu(self):
        """إنشاء قائمة التصفية المخصصة"""
        self.filter_menu = QMenu(self)
        self.filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.3);
                padding: 12px 0px;
                border-radius: 15px;
                margin: 3px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(96, 165, 250, 0.2);
                margin: 3px 15px;
                border: none;
            }
        """)

        # إضافة العناصر
        filter_options = [
            ("جميع الحالات", None),
            ("🟢 العملاء النشطين", "active"),
            ("🟡 العملاء العاديين", "normal"),
            ("🔴 العملاء المدينين", "debtor")
        ]

        for text, value in filter_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
            self.filter_menu.addAction(action)

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على أي مكان في الإطار"""
        if event.button() == Qt.LeftButton:
            # إضافة تأثير بصري للضغط
            self.status_filter_frame.setFocus()
            self.show_filter_menu()

    def show_filter_menu(self):
        """عرض قائمة التصفية"""
        # تحديد موقع القائمة تحت الإطار مباشرة
        frame_pos = self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft())
        self.filter_menu.exec_(frame_pos)

    def set_filter(self, value, text):
        """تعيين قيمة التصفية"""
        self.current_filter_value = value
        self.current_filter_label.setText(text)
        self.filter_clients()

    def create_advanced_clients_table(self):
        """إنشاء جدول العملاء المتطور والنظيف"""
        # إنشاء الجدول
        self.clients_table = QTableWidget()
        self.clients_table.setColumnCount(9)

        # عناوين الأعمدة مع الأيقونات الجديدة (الترتيب الجديد مع الملاحظات والتاريخ في النهاية)
        headers = [
            "🔢 ID",
            "🧑‍💼 اسم العميل",
            "🏠 العنوان",
            "📧 البريد الإلكتروني",
            "📱 رقم الهاتف",
            "💵 الرصيد",
            "⭐ حالة العميل",
            "📋 الملاحظات",
            "🗓️ تاريخ الإضافة"
        ]

        self.clients_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.clients_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.clients_table.setSelectionMode(QTableWidget.SingleSelection)
        self.clients_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.clients_table.setAlternatingRowColors(False)
        self.clients_table.setSortingEnabled(True)

        # إعدادات الصفوف والأعمدة
        self.clients_table.verticalHeader().setDefaultSectionSize(50)
        self.clients_table.verticalHeader().setVisible(False)

        header = self.clients_table.horizontalHeader()
        header.setFixedHeight(60)
        header.setDefaultAlignment(Qt.AlignCenter)

        # إضافة خاصية التكيف التلقائي مطابقة للفواتير مع مقاسات مخصصة
        header.setSectionResizeMode(QHeaderView.Stretch)

        # تعيين مقاسات ثابتة لأعمدة محددة مع الحفاظ على التكيف التلقائي للباقي
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # عمود ID
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # عمود اسم العميل
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # عمود العنوان
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # عمود البريد الإلكتروني
        self.clients_table.setColumnWidth(0, 120)  # ID - 120 بكسل
        self.clients_table.setColumnWidth(1, 300)  # اسم العميل - 300 بكسل
        self.clients_table.setColumnWidth(2, 300)  # العنوان - 300 بكسل
        self.clients_table.setColumnWidth(3, 250)  # البريد الإلكتروني - 250 بكسل

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.clients_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.clients_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)  # ارتفاع الصف الواحد
                scrollbar.setPageStep(200)   # 4 صفوف للصفحة
        except Exception:
            pass

        # تطبيق التصميم
        self.apply_table_style()
        self.add_watermark_to_table()
        self.setup_table_interactions()

    def apply_table_style(self):
        """تطبيق التصميم المتطور للجدول"""
        self.clients_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                outline: none;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                text-align: center;
                min-height: 30px;
                font-weight: bold;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9), stop:1 rgba(221, 214, 254, 0.9));
                color: white;
                border: 4px solid rgba(255, 255, 255, 0.9);
                border-left: 6px solid #fbbf24;
                border-right: 6px solid #fbbf24;
                border-radius: 18px;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            }
            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15), stop:1 rgba(196, 181, 253, 0.3));
                border: 3px solid rgba(102, 126, 234, 0.7);
                border-left: 6px solid #06b6d4;
                border-right: 6px solid #06b6d4;
                border-radius: 16px;
                color: #0f172a;
                font-weight: bold;
                transform: translateY(-1px);
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                height: 55px !important;
                letter-spacing: 1.3px !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5), 0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.5px !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #334155, stop:0.4 #1E40AF,
                    stop:0.6 #2563EB, stop:0.8 #4C1D95, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.2px !important;
                font-weight: 900 !important;
            }
        """)

    def add_watermark_to_table(self):
        """إضافة علامة مائية للجدول"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")
            painter.restore()

        original_paint = self.clients_table.paintEvent
        def new_paint_event(event):
            try:
                original_paint(event)
                painter = QPainter(self.clients_table.viewport())
                paint_watermark(painter, self.clients_table.viewport().rect())
                painter.end()
            except Exception:
                pass

        self.clients_table.paintEvent = new_paint_event

    def setup_table_interactions(self):
        """إعداد التفاعلات مع الجدول"""
        self.clients_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.clients_table.cellDoubleClicked.connect(self.on_cell_double_clicked)

        def mousePressEvent(event):
            item = self.clients_table.itemAt(event.pos())
            if item is None:
                self.clients_table.clearSelection()
            QTableWidget.mousePressEvent(self.clients_table, event)

        self.clients_table.mousePressEvent = mousePressEvent

        # إضافة معالج التمرير المخصص (يحاكي سلوك الأسهم)
        def wheelEvent(event):
            try:
                # التمرير العمودي بالماوس
                delta = event.angleDelta().y()

                # تجاهل الحركات الصغيرة جداً
                if abs(delta) < 120:
                    event.accept()
                    return

                # الحصول على شريط التمرير
                scrollbar = self.clients_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                # محاكاة سلوك الأسهم - خطوة واحدة في كل مرة
                if delta > 0:
                    # التمرير لأعلى - مثل الضغط على السهم العلوي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    # التمرير لأسفل - مثل الضغط على السهم السفلي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()

            except Exception:
                # في حالة الخطأ، استخدم التمرير الافتراضي
                QTableWidget.wheelEvent(self.clients_table, event)

        self.clients_table.wheelEvent = wheelEvent

    def on_selection_changed(self):
        """معالج تغيير التحديد في الجدول"""
        self.update_button_states()

    def on_cell_double_clicked(self, row, column):
        """معالج النقر المزدوج على خلية"""
        try:
            self.edit_client()
        except Exception as e:
            pass  # خطأ في النقر المزدوج

    def edit_client(self):
        """تعديل عميل عند النقر المزدوج"""
        try:
            client_id = self.get_selected_client_id()
            if client_id:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "تعديل عميل", f"سيتم إضافة نافذة تعديل العميل رقم {client_id} قريباً")
            else:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للتعديل")
        except Exception as e:
            pass  # خطأ في تعديل العميل

    def get_selected_client_id(self):
        """الحصول على معرف العميل المحدد"""
        try:
            current_row = self.clients_table.currentRow()
            if current_row >= 0:
                client_id_item = self.clients_table.item(current_row, 0)
                if client_id_item:
                    # استخراج الرقم من النص الذي يحتوي على أيقونة (مثل "🆔 1")
                    text = client_id_item.text()
                    # البحث عن الرقم في النص
                    import re
                    numbers = re.findall(r'\d+', text)
                    if numbers:
                        return int(numbers[0])
            return None
        except Exception as e:
            return None  # خطأ في الحصول على معرف العميل

    def refresh_data(self):
        """تحديث بيانات الجدول"""
        try:
            # جلب جميع العملاء من قاعدة البيانات (من الأقدم للأحدث)
            clients = self.session.query(Client).order_by(Client.id.asc()).all()
            self.populate_table(clients)
        except Exception as e:
            pass  # خطأ في تحميل البيانات

    def populate_table(self, clients):
        """ملء الجدول بالبيانات"""
        try:
            self.clients_table.setRowCount(len(clients))

            for row, client in enumerate(clients):
                # دالة مساعدة لإنشاء العناصر وتقليل التكرار
                def create_item(icon, text, default="No Data"):
                    display_text = text if text and text.strip() else default
                    item = QTableWidgetItem(f"{icon} {display_text}")
                    item.setTextAlignment(Qt.AlignCenter)
                    if display_text == default:
                        item.setForeground(QColor("#ef4444"))
                    return item

                # الرقم مع أيقونة حسب الرصيد - لون أسود للأرقام
                balance_value = client.balance or 0
                if balance_value > 0:
                    id_icon = "💰"
                elif balance_value < 0:
                    id_icon = "🔴"
                else:
                    id_icon = "🔢"

                # إنشاء عنصر ID مع لون أسود للرقم
                id_item = QTableWidgetItem(f"{id_icon} {client.id}")
                id_item.setTextAlignment(Qt.AlignCenter)
                id_item.setForeground(QColor("#000000"))  # لون أسود للرقم
                self.clients_table.setItem(row, 0, id_item)
                self.clients_table.setItem(row, 1, create_item("🧑‍💼", client.name))
                self.clients_table.setItem(row, 2, create_item("🏠", client.address))
                self.clients_table.setItem(row, 3, create_item("📧", client.email))
                self.clients_table.setItem(row, 4, create_item("📱", client.phone))

                # الرصيد مع ألوان حسب القيمة
                balance_text = format_currency(balance_value)
                if balance_value > 0:
                    balance_item = QTableWidgetItem(f"💰 {balance_text}")
                    balance_item.setForeground(QColor("#059669"))
                elif balance_value < 0:
                    balance_item = QTableWidgetItem(f"💸 {balance_text}")
                    balance_item.setForeground(QColor("#dc2626"))
                else:
                    balance_item = QTableWidgetItem(f"💵 {balance_text}")
                    balance_item.setForeground(QColor("#000000"))
                balance_item.setTextAlignment(Qt.AlignCenter)
                self.clients_table.setItem(row, 5, balance_item)

                # حالة العميل
                status_item = QTableWidgetItem(self.get_client_status(balance_value))
                status_item.setTextAlignment(Qt.AlignCenter)
                self.clients_table.setItem(row, 6, status_item)

                # الملاحظات والتاريخ
                notes_text = client.notes if hasattr(client, 'notes') and client.notes and client.notes.strip() else None
                date_text = client.created_at.strftime("%Y-%m-%d") if hasattr(client, 'created_at') and client.created_at else None

                self.clients_table.setItem(row, 7, create_item("📋", notes_text))
                self.clients_table.setItem(row, 8, create_item("🗓️", date_text))

        except Exception as e:
            pass  # خطأ في عرض البيانات

    def get_client_status(self, balance):
        """تحديد حالة العميل بناءً على الرصيد"""
        if balance > 0:
            return "🟢 نشط"
        elif balance == 0:
            return "🟡 عادي"
        else:
            return "🔴 مدين"

    def filter_clients(self):
        """تصفية العملاء بناءً على نص البحث والحالة"""
        try:
            search_text = self.search_edit.text().strip().lower()
            status = getattr(self, 'current_filter_value', None)

            # بناء الاستعلام
            query = self.session.query(Client)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Client.name.like(f"%{search_text}%") |
                    Client.phone.like(f"%{search_text}%") |
                    Client.email.like(f"%{search_text}%") |
                    Client.address.like(f"%{search_text}%")
                )

            # تطبيق تصفية الحالة
            if status == "active":
                query = query.filter(Client.balance > 0)
            elif status == "normal":
                query = query.filter(Client.balance == 0)
            elif status == "debtor":
                query = query.filter(Client.balance < 0)

            # تنفيذ الاستعلام (من الأقدم للأحدث)
            clients = query.order_by(Client.id.asc()).all()

            # تحديث الجدول
            self.populate_table(clients)

        except Exception as e:
            pass  # خطأ في تصفية البيانات

    def update_button_states(self):
        """تحديث حالة الأزرار حسب التحديد مع تأثيرات الظهور"""
        try:
            has_selection = len(self.clients_table.selectedItems()) > 0

            # الأزرار التي تحتاج تحديد مع تأثير الظهور
            self.set_button_visibility(self.edit_button, has_selection)
            self.set_button_visibility(self.delete_button, has_selection)
            self.set_button_visibility(self.view_button, has_selection)

            # الأزرار المتاحة دائماً مع تأثير الظهور
            self.set_button_visibility(self.add_button, True)
            self.set_button_visibility(self.refresh_button, True)
            self.set_button_visibility(self.export_button, True)
            self.set_button_visibility(self.statistics_button, True)
        except Exception as e:
            # في حالة الخطأ، تفعيل جميع الأزرار
            try:
                self.add_button.setEnabled(True)
                self.edit_button.setEnabled(True)
                self.delete_button.setEnabled(True)
                self.refresh_button.setEnabled(True)
                self.view_button.setEnabled(True)
                self.export_button.setEnabled(True)
                self.statistics_button.setEnabled(True)
            except:
                pass

    def set_button_visibility(self, button, enabled):
        """تعيين حالة الزر مع تأثير الظهور/الاختفاء السلس"""
        try:
            button.setEnabled(enabled)

            if enabled:
                # إظهار الزر بشفافية كاملة
                print(f"🟢 تفعيل الزر: {button.text()}")
                # إزالة أي تأثيرات شفافية سابقة وتطبيق الشفافية الكاملة
                current_style = button.styleSheet()
                # إزالة أي opacity موجودة
                import re
                clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                # إضافة opacity كاملة
                new_style = clean_style + "\nQPushButton { opacity: 1.0; }"
                button.setStyleSheet(new_style)
                button.show()
            else:
                # تقليل شفافية الزر (لا نخفيه تماماً)
                print(f"🔴 تعطيل الزر: {button.text()}")
                current_style = button.styleSheet()
                # إزالة أي opacity موجودة
                import re
                clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                # إضافة opacity منخفضة
                new_style = clean_style + "\nQPushButton { opacity: 0.3; }"
                button.setStyleSheet(new_style)

        except Exception as e:
            # في حالة الخطأ، استخدم الطريقة التقليدية
            button.setEnabled(enabled)

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#0d9488', 'hover_mid': '#14b8a6', 'hover_end': '#2dd4bf', 'hover_bottom': '#5eead4',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.6)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#0891b2',
                    'hover_start': '#0891b2', 'hover_mid': '#06b6d4', 'hover_end': '#22d3ee', 'hover_bottom': '#67e8f9',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.6)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#be185d',
                    'hover_start': '#be185d', 'hover_mid': '#ec4899', 'hover_end': '#f472b6', 'hover_bottom': '#f9a8d4',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.6)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#4338ca',
                    'hover_start': '#4338ca', 'hover_mid': '#6366f1', 'hover_end': '#818cf8', 'hover_bottom': '#a5b4fc',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4338ca', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.6)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#c2410c',
                    'hover_start': '#c2410c', 'hover_mid': '#ea580c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#ea580c', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#ea580c', 'text': '#ffffff', 'shadow': 'rgba(234, 88, 12, 0.6)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']};
                    letter-spacing: 1px;
                    text-transform: uppercase;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 5px solid {color_scheme['hover_border']};
                    transform: translateY(-3px) scale(1.02);
                    box-shadow: 0 10px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 30px {color_scheme['shadow']};
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               1px 1px 3px rgba(0, 0, 0, 0.7);
                    letter-spacing: 1.2px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px) scale(0.98);
                    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6),
                               0 3px 6px {color_scheme['shadow']},
                               inset 0 0 15px rgba(0, 0, 0, 0.4);
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 1.0),
                               1px 1px 2px rgba(0, 0, 0, 0.8);
                    letter-spacing: 0.8px;
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #9ca3af, stop:0.5 #6b7280, stop:1 #4b5563);
                    color: #d1d5db;
                    border: 3px solid #6b7280;
                    box-shadow: none;
                    text-shadow: none;
                    text-transform: none;
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception as e:
            pass  # خطأ في تطبيق تصميم الزر

    # دوال الأزرار
    def add_client(self):
        """إضافة عميل جديد"""
        try:
            dialog = AddClientDialog(self.session, self)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()  # تحديث الجدول بعد الإضافة
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إضافة العميل: {str(e)}")

    def delete_client(self):
        """حذف عميل"""
        try:
            client_id = self.get_selected_client_id()
            if client_id:
                print(f"حذف العميل رقم: {client_id}")
                from PyQt5.QtWidgets import QMessageBox
                reply = QMessageBox.question(
                    self,
                    "تأكيد الحذف",
                    f"هل أنت متأكد من حذف العميل رقم {client_id}؟",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                if reply == QMessageBox.Yes:
                    QMessageBox.information(self, "حذف العميل", f"تم حذف العميل رقم {client_id} بنجاح")
                    self.refresh_data()
            else:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للحذف")
        except Exception as e:
            pass  # خطأ في حذف العميل

    def view_client(self):
        """عرض تفاصيل العميل"""
        try:
            client_id = self.get_selected_client_id()
            if client_id:
                print(f"عرض تفاصيل العميل رقم: {client_id}")
                client = self.session.query(Client).filter(Client.id == client_id).first()
                if client:
                    from PyQt5.QtWidgets import QMessageBox
                    details = f"""
تفاصيل العميل:

🆔 الرقم: {client.id}
👨‍💼 الاسم: {client.name or 'No Data'}
📍 العنوان: {client.address or 'No Data'}
📧 البريد: {client.email or 'No Data'}
📞 الهاتف: {client.phone or 'No Data'}
💰 الرصيد: {format_currency(client.balance or 0)}
📝 الملاحظات: {client.notes or 'No Data'}
                    """
                    QMessageBox.information(self, f"تفاصيل العميل - {client.name or 'No Data'}", details)
                else:
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.warning(self, "خطأ", "لم يتم العثور على العميل")
            else:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل لعرض تفاصيله")
        except Exception as e:
            pass  # خطأ في عرض تفاصيل العميل

    def export_to_excel(self):
        """تصدير بيانات العملاء إلى Excel"""
        try:
            import csv
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel", "العملاء.csv", "CSV Files (*.csv)"
            )

            if not file_path:
                return

            # جلب جميع العملاء
            clients = self.session.query(Client).order_by(Client.id.asc()).all()

            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # كتابة العناوين
                headers = [
                    "الرقم التسلسلي",
                    "اسم العميل",
                    "العنوان",
                    "البريد الإلكتروني",
                    "رقم الهاتف",
                    "الرصيد",
                    "حالة العميل",
                    "الملاحظات",
                    "تاريخ الإضافة"
                ]
                writer.writerow(headers)

                # كتابة البيانات
                for client in clients:
                    # تحديد حالة العميل
                    balance = client.balance or 0
                    if balance > 0:
                        status_text = "نشط"
                    elif balance == 0:
                        status_text = "عادي"
                    else:
                        status_text = "مدين"

                    writer.writerow([
                        client.id,
                        client.name or "No Data",
                        client.address or "No Data",
                        client.email or "No Data",
                        client.phone or "No Data",
                        f"{balance:.2f} ج.م",
                        status_text,
                        client.notes or "No Data",
                        client.created_at.strftime("%Y-%m-%d %H:%M") if client.created_at else "No Data"
                    ])

                from utils.message_utils import show_info_message
                show_info_message("تم", f"تم تصدير العملاء بنجاح إلى:\n{file_path}")

        except Exception as e:
            from utils.message_utils import show_error_message
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def export_to_csv(self):
        """تصدير بيانات العملاء إلى CSV"""
        try:
            import csv
            from PyQt5.QtWidgets import QFileDialog

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف CSV", "قائمة_العملاء.csv", "ملفات CSV (*.csv)")

            if not file_path:
                return

            # جلب جميع العملاء
            clients = self.session.query(Client).order_by(Client.id.asc()).all()

            # إعداد البيانات
            headers = ["الرقم التسلسلي", "اسم العميل", "العنوان", "البريد الإلكتروني", "رقم الهاتف", "الرصيد", "حالة العميل", "الملاحظات", "تاريخ الإضافة"]
            data = []

            for client in clients:
                # تحديد حالة العميل
                balance = client.balance or 0
                if balance > 0:
                    status_text = "نشط"
                elif balance == 0:
                    status_text = "عادي"
                else:
                    status_text = "مدين"

                data.append([
                    client.id,
                    client.name or "No Data",
                    client.address or "No Data",
                    client.email or "No Data",
                    client.phone or "No Data",
                    f"{balance:.2f} ج.م",
                    status_text,
                    client.notes or "No Data",
                    client.created_at.strftime("%Y-%m-%d %H:%M") if client.created_at else "No Data"
                ])

            # كتابة البيانات إلى الملف
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(data)

            from utils.message_utils import show_info_message
            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            from utils.message_utils import show_error_message
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def show_statistics(self):
        """عرض الإحصائيات"""
        try:
            total_clients = self.session.query(Client).count()
            active_clients = self.session.query(Client).filter(Client.balance > 0).count()
            normal_clients = self.session.query(Client).filter(Client.balance == 0).count()
            debtor_clients = self.session.query(Client).filter(Client.balance < 0).count()

            total_balance = sum([c.balance or 0 for c in self.session.query(Client).all()])

            from PyQt5.QtWidgets import QMessageBox
            stats = f"""
📊 إحصائيات العملاء:

👥 إجمالي العملاء: {total_clients}
🟢 العملاء النشطين: {active_clients}
🟡 العملاء العاديين: {normal_clients}
🔴 العملاء المدينين: {debtor_clients}

💰 إجمالي الأرصدة: {format_currency(total_balance)}
            """
            QMessageBox.information(self, "إحصائيات العملاء", stats)
        except Exception as e:
            pass  # خطأ في عرض الإحصائيات

    def create_sample_data_if_empty(self):
        """إنشاء بيانات تجريبية إذا كانت قاعدة البيانات فارغة"""
        try:
            # التحقق من وجود عملاء
            existing_clients = self.session.query(Client).count()

            if existing_clients == 0:
                # إنشاء عملاء تجريبيين
                sample_clients = [
                    Client(
                        name='أحمد محمد السعيد',
                        phone='0501234567',
                        email='<EMAIL>',
                        address='الرياض - حي النخيل - شارع الملك فهد',
                        balance=15000.0,
                        notes='عميل VIP - نشط جداً'
                    ),
                    Client(
                        name='فاطمة علي الزهراني',
                        phone='0509876543',
                        email='<EMAIL>',
                        address='جدة - حي الصفا - طريق الأمير سلطان',
                        balance=8500.0,
                        notes='عميل مميز - دفعات منتظمة'
                    ),
                    Client(
                        name='محمد سالم القحطاني',
                        phone='0551122334',
                        email='<EMAIL>',
                        address='الدمام - حي الفيصلية - شارع الخليج',
                        balance=-2500.0,
                        notes='عميل مدين - يحتاج متابعة'
                    ),
                    Client(
                        name='نورا خالد العتيبي',
                        phone='0556677889',
                        email='<EMAIL>',
                        address='مكة المكرمة - العزيزية - طريق الحرم',
                        balance=0.0,
                        notes='عميل جديد - تم التسجيل حديثاً'
                    ),
                    Client(
                        name='عبدالله أحمد الغامدي',
                        phone='0544332211',
                        email='<EMAIL>',
                        address='المدينة المنورة - حي العوالي',
                        balance=12000.0,
                        notes='عميل موثوق - تعامل طويل الأمد'
                    ),
                    Client(
                        name='سارة عبدالرحمن النجار',
                        phone='0533445566',
                        email='<EMAIL>',
                        address='الطائف - حي الشفا - طريق الهدا',
                        balance=-1200.0,
                        notes='عميل مدين - مبلغ صغير'
                    )
                ]

                for client in sample_clients:
                    self.session.add(client)

                self.session.commit()

                # تحديث الجدول
                self.refresh_data()

        except Exception as e:
            self.session.rollback()  # خطأ في إنشاء البيانات التجريبية


class AddClientDialog(QDialog):
    """نافذة إضافة عميل جديد مع نظام تعدد الأرقام"""

    def __init__(self, session, parent=None):
        super().__init__(parent)
        self.session = session
        self.parent_widget = parent  # حفظ مرجع للوالد
        self.setWindowTitle("إضافة عميل جديد")
        self.setModal(True)
        self.resize(650, 650)  # جعل النافذة مربعة
        self.setup_ui()

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور على الأزرار - نسخة مبسطة لنافذة الحوار"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم بسيط كبديل
                colors = {
                    'emerald': '#10b981',
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 10px 20px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def setup_ui(self):
        """إعداد واجهة النافذة - شريط العنوان في مكانه الطبيعي"""
        # استخدام شريط العنوان الطبيعي للنظام مع النص في المنتصف
        self.setWindowTitle("🤝 إضافة عميل جديد - نظام إدارة العملاء المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # خلفية النافذة الرئيسية بدون إطار مع تدرجات متطورة جديدة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.98),
                    stop:0.15 rgba(30, 41, 59, 0.95),
                    stop:0.3 rgba(37, 99, 235, 0.1),
                    stop:0.45 rgba(51, 65, 85, 0.9),
                    stop:0.6 rgba(59, 130, 246, 0.15),
                    stop:0.75 rgba(71, 85, 105, 0.92),
                    stop:0.9 rgba(96, 165, 250, 0.2),
                    stop:1 rgba(148, 163, 184, 0.95));
                border: none;
                border-radius: 15px;
            }
        """)

        # تخطيط المحتوى الرئيسي للنافذة
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(18)


        # نموذج البيانات مع تصميم محسن
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)
        form_layout.setFormAlignment(Qt.AlignLeft | Qt.AlignTop)
        form_layout.setHorizontalSpacing(15)
        form_layout.setVerticalSpacing(12)

        # إنشاء دالة لتصميم النصوص مع عرض مقلل
        def create_styled_label(text, icon, required=False):
            label = QLabel(f"{icon} {text}")
            if required:
                label.setText(f"{icon} {text} *")
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 5px;
                    padding: 8px 12px;
                    font-weight: bold;
                    font-size: 16px;
                    min-width: 120px;
                    max-width: 120px;
                    text-align: center;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            return label

        # اسم العميل (مطلوب) - مع استغلال أفضل للمساحة
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم العميل الكامل هنا... (مطلوب)")
        self.name_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("اسم العميل", "👤", True), self.name_edit)

        # العنوان - مع استغلال أفضل للمساحة
        self.address_edit = QLineEdit()
        self.address_edit.setPlaceholderText("أدخل العنوان الكامل للعميل (الشارع، المدينة، المحافظة)...")
        self.address_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("العنوان", "🏠"), self.address_edit)

        # البريد الإلكتروني - مع استغلال أفضل للمساحة
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("أدخل البريد الإلكتروني الكامل (<EMAIL>)...")
        self.email_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("البريد الإلكتروني", "📧"), self.email_edit)

        # أرقام الهاتف (نظام تعدد الأرقام)
        phone_container = QVBoxLayout()

        # الهاتف الأساسي - مع استغلال أفضل للمساحة
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف الأساسي (مثال: 01234567890)...")
        self.phone_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        phone_container.addWidget(self.phone_edit)

        # أرقام إضافية
        self.additional_phones = []
        self.phone_widgets = []

        # زر إضافة رقم هاتف مع ارتفاع مقلل ومنزل للأسفل
        add_phone_btn = QPushButton("➕ إضافة رقم")
        self.style_advanced_button(add_phone_btn, 'info')
        add_phone_btn.clicked.connect(self.add_phone_field)

        # تقليل ارتفاع الزر وإنزاله للأسفل
        add_phone_btn.setStyleSheet(add_phone_btn.styleSheet() + """
            QPushButton {
                margin-top: 15px;
                margin-bottom: 8px;
                margin-left: 5px;
                margin-right: 5px;
                max-height: 35px;
                min-height: 35px;
                padding: 6px 12px;
                font-size: 14px;
            }
        """)

        phone_container.addWidget(add_phone_btn)

        form_layout.addRow(create_styled_label("أرقام الهاتف", "📱"), phone_container)

        # الرصيد الابتدائي - مع استغلال أفضل للمساحة
        self.balance_spinbox = QDoubleSpinBox()
        self.balance_spinbox.setRange(-999999999, 999999999)
        self.balance_spinbox.setDecimals(0)  # إزالة الأرقام العشرية
        self.balance_spinbox.setValue(0)
        self.balance_spinbox.setSuffix(" جنيه")
        self.balance_spinbox.setButtonSymbols(QDoubleSpinBox.NoButtons)  # إزالة الأسهم
        self.balance_spinbox.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 18px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("الرصيد الابتدائي", "💰"), self.balance_spinbox)

        # الملاحظات - مع استغلال أفضل للمساحة
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات أو تفاصيل إضافية عن العميل هنا...")
        self.notes_edit.setMaximumHeight(100)  # ارتفاع أكبر قليلاً
        self.notes_edit.setMinimumWidth(350)   # عرض أكبر
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("الملاحظات", "📝"), self.notes_edit)

        layout.addLayout(form_layout)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        # زر الحفظ مطابق للفواتير
        save_button = QPushButton("💾 حفظ")
        self.style_advanced_button(save_button, 'emerald')
        save_button.clicked.connect(self.save_client)

        # زر الإلغاء - أحمر للخطر
        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'danger')
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)
        layout.addLayout(buttons_layout)

    def add_phone_field(self):
        """إضافة حقل رقم هاتف إضافي مطور"""
        try:
            # إنشاء حاوي للرقم الجديد مع إطار
            phone_widget = QWidget()
            phone_widget.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(139, 92, 246, 0.1),
                        stop:0.5 rgba(124, 58, 237, 0.15),
                        stop:1 rgba(109, 40, 217, 0.1));
                    border: 2px solid rgba(139, 92, 246, 0.5);
                    border-radius: 10px;
                    margin: 5px;
                    padding: 8px;
                }
            """)

            phone_container = QHBoxLayout(phone_widget)
            phone_container.setContentsMargins(10, 8, 10, 8)
            phone_container.setSpacing(12)

            # نص تعريفي للرقم الإضافي - مطور
            phone_label = QLabel(f"📱 رقم إضافي {len(self.additional_phones) + 1}:")
            phone_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(139, 92, 246, 0.9),
                        stop:0.2 rgba(124, 58, 237, 0.8),
                        stop:0.4 rgba(109, 40, 217, 0.7),
                        stop:0.6 rgba(91, 33, 182, 0.8),
                        stop:0.8 rgba(76, 29, 149, 0.9),
                        stop:1 rgba(59, 7, 100, 0.8));
                    border: 3px solid rgba(139, 92, 246, 1.0);
                    border-radius: 8px;
                    padding: 10px 15px;
                    font-weight: bold;
                    font-size: 16px;
                    min-width: 140px;
                    max-width: 140px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.6),
                               inset 0 2px 0 rgba(255, 255, 255, 0.2),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3);
                }
            """)
            phone_label.setAlignment(Qt.AlignCenter)

            # حقل الرقم مطور
            phone_edit = QLineEdit()
            phone_edit.setPlaceholderText(f"أدخل رقم الهاتف الإضافي {len(self.additional_phones) + 1}...")
            phone_edit.setStyleSheet("""
                QLineEdit {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.95),
                        stop:0.2 rgba(248, 250, 252, 0.9),
                        stop:0.4 rgba(241, 245, 249, 0.85),
                        stop:0.6 rgba(248, 250, 252, 0.9),
                        stop:0.8 rgba(255, 255, 255, 0.95),
                        stop:1 rgba(226, 232, 240, 0.9));
                    border: 3px solid rgba(139, 92, 246, 0.7);
                    border-radius: 12px;
                    padding: 12px 16px;
                    font-size: 15px;
                    font-weight: bold;
                    color: #1f2937;
                    min-height: 28px;
                    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3),
                               inset 0 1px 0 rgba(255, 255, 255, 0.5);
                }
                QLineEdit:focus {
                    border: 4px solid rgba(139, 92, 246, 1.0);
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(245, 243, 255, 0.95),
                        stop:0.2 rgba(237, 233, 254, 0.9),
                        stop:0.4 rgba(221, 214, 254, 0.85),
                        stop:0.6 rgba(237, 233, 254, 0.9),
                        stop:0.8 rgba(245, 243, 255, 0.95),
                        stop:1 rgba(255, 255, 255, 0.9));
                    box-shadow: 0 6px 16px rgba(139, 92, 246, 0.5),
                               inset 0 2px 0 rgba(255, 255, 255, 0.6);
                    transform: translateY(-1px);
                }
            """)

            # زر الحذف مطور
            remove_btn = QPushButton("🗑️ حذف")
            self.style_advanced_button(remove_btn, 'danger')
            remove_btn.clicked.connect(lambda: self.remove_phone_field(phone_widget, phone_edit))

            phone_container.addWidget(phone_edit)
            phone_container.addWidget(remove_btn)

            # إضافة إلى القائمة
            self.additional_phones.append(phone_edit)
            self.phone_widgets.append(phone_container)

            # إضافة إلى الواجهة
            form_layout = self.layout().itemAt(1).layout()  # الحصول على form_layout
            phone_row = None
            for i in range(form_layout.rowCount()):
                if form_layout.itemAt(i, QFormLayout.LabelRole):
                    label = form_layout.itemAt(i, QFormLayout.LabelRole).widget()
                    if label and "أرقام الهاتف" in label.text():
                        phone_row = i
                        break

            if phone_row is not None:
                # إضافة الحقل الجديد بعد حقول الهاتف الموجودة
                widget = QWidget()
                widget.setLayout(phone_container)
                form_layout.insertRow(phone_row + len(self.phone_widgets), "", widget)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في إضافة حقل الهاتف: {str(e)}")

    def remove_phone_field(self, container, phone_edit):
        """حذف حقل رقم هاتف"""
        try:
            # إزالة من القوائم
            if phone_edit in self.additional_phones:
                self.additional_phones.remove(phone_edit)
            if container in self.phone_widgets:
                self.phone_widgets.remove(container)

            # إزالة من الواجهة
            while container.count():
                child = container.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()

            # إزالة الحاوي نفسه
            container.deleteLater()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ في حذف حقل الهاتف: {str(e)}")

    def save_client(self):
        """حفظ العميل الجديد مع نظام تعدد الأرقام"""
        try:
            # التحقق من صحة البيانات
            name = self.name_edit.text().strip()
            if not name:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم العميل")
                self.name_edit.setFocus()
                return

            # جمع أرقام الهاتف
            phone_numbers = []

            # الرقم الأساسي
            main_phone = self.phone_edit.text().strip()
            if main_phone:
                phone_numbers.append(main_phone)

            # الأرقام الإضافية
            for phone_edit in self.additional_phones:
                additional_phone = phone_edit.text().strip()
                if additional_phone:
                    phone_numbers.append(additional_phone)

            # تحويل قائمة الأرقام إلى نص مفصول بفواصل
            phone_string = ", ".join(phone_numbers) if phone_numbers else None

            # إنشاء عميل جديد
            new_client = Client(
                name=name,
                address=self.address_edit.text().strip() or None,
                email=self.email_edit.text().strip() or None,
                phone=phone_string,  # حفظ جميع الأرقام
                balance=self.balance_spinbox.value(),
                notes=self.notes_edit.toPlainText().strip() or None
            )

            # حفظ في قاعدة البيانات
            self.session.add(new_client)
            self.session.commit()

            # رسالة نجاح مع تفاصيل الأرقام
            phone_info = f"\nأرقام الهاتف: {phone_string}" if phone_string else "\nلم يتم إدخال أرقام هاتف"
            QMessageBox.information(
                self,
                "نجح",
                f"تم إضافة العميل '{name}' بنجاح!{phone_info}"
            )
            self.accept()

        except Exception as e:
            self.session.rollback()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ العميل: {str(e)}")

    def mousePressEvent(self, event):
        """بداية سحب النافذة"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """سحب النافذة"""
        if event.buttons() == Qt.LeftButton and self.drag_position:
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def customize_title_bar(self):
        """تخصيص شريط العنوان الطبيعي مع التدرجات والألوان الجديدة المتطورة"""
        try:
            # إنشاء أيقونة مخصصة متطورة مع تدرجات جديدة
            pixmap = QPixmap(48, 48)  # حجم أكبر للوضوح
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setRenderHint(QPainter.SmoothPixmapTransform)

            # إنشاء تدرج متطور للأيقونة
            from PyQt5.QtGui import QRadialGradient

            gradient = QRadialGradient(24, 24, 20)
            # تدرج متطور مع ألوان جديدة
            gradient.setColorAt(0.0, QColor(15, 23, 42))      # أزرق داكن عميق
            gradient.setColorAt(0.2, QColor(30, 41, 59))      # أزرق رمادي
            gradient.setColorAt(0.4, QColor(37, 99, 235))     # أزرق حيوي
            gradient.setColorAt(0.6, QColor(59, 130, 246))    # أزرق فاتح
            gradient.setColorAt(0.8, QColor(96, 165, 250))    # أزرق ساطع
            gradient.setColorAt(1.0, QColor(147, 197, 253))   # أزرق فاتح جداً

            brush = QBrush(gradient)
            painter.setBrush(brush)

            # إضافة حدود ذهبية متطورة
            from PyQt5.QtGui import QPen
            pen = QPen(QColor(251, 191, 36), 3)  # ذهبي
            pen.setStyle(Qt.SolidLine)
            painter.setPen(pen)

            # رسم دائرة متطورة مع ظل
            painter.drawEllipse(3, 3, 42, 42)

            # إضافة تأثير داخلي
            inner_gradient = QRadialGradient(24, 24, 15)
            inner_gradient.setColorAt(0.0, QColor(255, 255, 255, 80))
            inner_gradient.setColorAt(1.0, QColor(255, 255, 255, 0))

            painter.setBrush(QBrush(inner_gradient))
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(8, 8, 32, 32)

            # إضافة رمز العميل في المنتصف
            painter.setPen(QColor(255, 255, 255))
            font = painter.font()
            font.setPointSize(20)
            font.setBold(True)
            painter.setFont(font)
            painter.drawText(pixmap.rect(), Qt.AlignCenter, "🤝")

            painter.end()

            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تدرجات متطورة على شريط العنوان (Windows فقط)
            self.apply_advanced_title_bar_styling()

        except Exception as e:
            # في حالة فشل إنشاء الأيقونة، تجاهل الخطأ
            print(f"تحذير: فشل في إنشاء الأيقونة المتطورة: {e}")

        # تعيين خصائص النافذة المتطورة
        self.setModal(True)
        self.setMinimumSize(700, 700)  # حجم أكبر قليلاً
        self.setMaximumSize(900, 900)  # حجم أقصى أكبر

        # تمكين إغلاق النافذة بـ Escape
        escape_shortcut = QShortcut(QKeySequence(Qt.Key_Escape), self)
        escape_shortcut.activated.connect(self.reject)

        # إضافة اختصارات إضافية
        enter_shortcut = QShortcut(QKeySequence(Qt.Key_Return), self)
        enter_shortcut.activated.connect(self.save_client)

        ctrl_s_shortcut = QShortcut(QKeySequence("Ctrl+S"), self)
        ctrl_s_shortcut.activated.connect(self.save_client)

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان مع النص في المنتصف"""
        try:
            import platform
            if platform.system() == "Windows":
                # تطبيق ألوان متطورة لشريط العنوان في Windows
                import ctypes
                from ctypes import wintypes

                # الحصول على handle النافذة
                hwnd = int(self.winId())

                # تعيين لون شريط العنوان (أزرق داكن متطور)
                DWMWA_CAPTION_COLOR = 35
                color = 0x002A1E0F  # أزرق داكن بتنسيق BGR

                ctypes.windll.dwmapi.DwmSetWindowAttribute(
                    hwnd,
                    DWMWA_CAPTION_COLOR,
                    ctypes.byref(wintypes.DWORD(color)),
                    ctypes.sizeof(wintypes.DWORD)
                )

                # تعيين لون النص (أبيض ساطع مع تحسين الوضوح)
                DWMWA_TEXT_COLOR = 36
                text_color = 0x00FFFFFF  # أبيض ساطع

                ctypes.windll.dwmapi.DwmSetWindowAttribute(
                    hwnd,
                    DWMWA_TEXT_COLOR,
                    ctypes.byref(wintypes.DWORD(text_color)),
                    ctypes.sizeof(wintypes.DWORD)
                )

                # تمكين الشفافية والتأثيرات المتطورة
                DWMWA_USE_IMMERSIVE_DARK_MODE = 20
                dark_mode = wintypes.DWORD(1)

                ctypes.windll.dwmapi.DwmSetWindowAttribute(
                    hwnd,
                    DWMWA_USE_IMMERSIVE_DARK_MODE,
                    ctypes.byref(dark_mode),
                    ctypes.sizeof(dark_mode)
                )

        except Exception as e:
            # في حالة فشل تطبيق التصميم المتطور، تجاهل الخطأ
            print(f"تحذير: فشل في تطبيق التصميم المتطور لشريط العنوان: {e}")

        # تحسين محاذاة النص في المنتصف (عبر تحديث العنوان)
        self.center_title_text()

    def center_title_text(self):
        """تحسين وضع النص في منتصف شريط العنوان"""
        try:
            # إضافة مسافات لتوسيط النص بصرياً
            original_title = "🤝 إضافة عميل جديد - نظام إدارة العملاء المتطور والشامل"

            # حساب المسافات المطلوبة للتوسيط
            padding_spaces = "    "  # مسافات إضافية للتوسيط
            centered_title = f"{padding_spaces}{original_title}{padding_spaces}"

            # تحديث العنوان مع التوسيط
            self.setWindowTitle(centered_title)

        except Exception as e:
            print(f"تحذير: فشل في توسيط النص: {e}")


