"""
تحسينات الأداء للتطبيق
"""

import gc
import threading
from PyQt5.QtCore import QTimer, QThread, pyqtSignal
from PyQt5.QtWidgets import QApplication

class LazyDataLoader(QThread):
    """محمل البيانات المؤجل لتحسين الأداء"""
    data_loaded = pyqtSignal(str, object)
    
    def __init__(self, session):
        super().__init__()
        self.session = session
        self.load_queue = []
        
    def add_to_queue(self, table_name, query_func):
        """إضافة استعلام إلى قائمة التحميل"""
        self.load_queue.append((table_name, query_func))
        
    def run(self):
        """تشغيل تحميل البيانات في الخلفية"""
        for table_name, query_func in self.load_queue:
            try:
                data = query_func()
                self.data_loaded.emit(table_name, data)
                # توقف قصير لتجنب حجب الواجهة
                self.msleep(50)
            except Exception as e:
                print(f"خطأ في تحميل بيانات {table_name}: {str(e)}")

class PerformanceOptimizer:
    """محسن الأداء الرئيسي"""
    
    @staticmethod
    def optimize_table_loading(table_widget):
        """تحسين تحميل الجداول"""
        # تعطيل التحديث أثناء التحميل
        table_widget.setUpdatesEnabled(False)
        
        # تعطيل الفرز أثناء التحميل
        table_widget.setSortingEnabled(False)
        
        # تقليل عدد الأعمدة المرئية مؤقتاً
        return table_widget
    
    @staticmethod
    def restore_table_performance(table_widget):
        """استعادة إعدادات الجدول بعد التحميل"""
        # إعادة تفعيل التحديث
        table_widget.setUpdatesEnabled(True)
        
        # إعادة تفعيل الفرز
        table_widget.setSortingEnabled(True)
        
        # تحديث الجدول مرة واحدة
        table_widget.update()
    
    @staticmethod
    def batch_insert_rows(table_widget, data, batch_size=50):
        """إدراج الصفوف على دفعات لتحسين الأداء"""
        total_rows = len(data)
        
        for i in range(0, total_rows, batch_size):
            batch = data[i:i + batch_size]
            
            # إدراج دفعة من الصفوف
            for row_data in batch:
                # منطق إدراج الصف هنا
                pass
            
            # تحديث الواجهة كل دفعة
            QApplication.processEvents()
    
    @staticmethod
    def reduce_memory_usage():
        """تقليل استخدام الذاكرة"""
        # تشغيل جامع القمامة
        collected = gc.collect()
        
        # تحرير الذاكرة غير المستخدمة
        try:
            import ctypes
            if hasattr(ctypes, 'windll'):
                ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1)
        except:
            pass
            
        return collected

class SmartCacheManager:
    """مدير التخزين المؤقت الذكي"""
    
    def __init__(self, max_size=100):
        self.cache = {}
        self.max_size = max_size
        self.access_count = {}
    
    def get(self, key):
        """الحصول على قيمة من التخزين المؤقت"""
        if key in self.cache:
            self.access_count[key] = self.access_count.get(key, 0) + 1
            return self.cache[key]
        return None
    
    def set(self, key, value):
        """تعيين قيمة في التخزين المؤقت"""
        if len(self.cache) >= self.max_size:
            # إزالة العنصر الأقل استخداماً
            least_used = min(self.access_count.items(), key=lambda x: x[1])
            del self.cache[least_used[0]]
            del self.access_count[least_used[0]]
        
        self.cache[key] = value
        self.access_count[key] = 1
    
    def clear(self):
        """مسح التخزين المؤقت"""
        self.cache.clear()
        self.access_count.clear()

# مثيل عام لمدير التخزين المؤقت
cache_manager = SmartCacheManager()

def apply_startup_optimizations():
    """تطبيق تحسينات بدء التشغيل"""
    # تقليل استخدام الذاكرة
    PerformanceOptimizer.reduce_memory_usage()
    
    # تعطيل جمع القمامة التلقائي مؤقتاً
    gc.disable()
    
    print("✅ تم تطبيق تحسينات بدء التشغيل")

def restore_normal_operations():
    """استعادة العمليات العادية بعد التحميل"""
    # إعادة تفعيل جمع القمامة
    gc.enable()
    
    # تشغيل جمع القمامة مرة واحدة
    gc.collect()
    
    print("✅ تم استعادة العمليات العادية")

class ProgressiveLoader:
    """محمل تدريجي للبيانات الكبيرة"""
    
    def __init__(self, table_widget, data_source, batch_size=25):
        self.table_widget = table_widget
        self.data_source = data_source
        self.batch_size = batch_size
        self.current_batch = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.load_next_batch)
    
    def start_loading(self):
        """بدء التحميل التدريجي"""
        self.current_batch = 0
        self.timer.start(10)  # تحميل كل 10 مللي ثانية
    
    def load_next_batch(self):
        """تحميل الدفعة التالية"""
        start_idx = self.current_batch * self.batch_size
        end_idx = start_idx + self.batch_size
        
        batch_data = self.data_source[start_idx:end_idx]
        
        if not batch_data:
            # انتهى التحميل
            self.timer.stop()
            PerformanceOptimizer.restore_table_performance(self.table_widget)
            return
        
        # تحميل الدفعة الحالية
        for row_data in batch_data:
            # منطق إضافة الصف هنا
            pass
        
        self.current_batch += 1
        
        # تحديث الواجهة
        QApplication.processEvents()

def optimize_database_queries(session):
    """تحسين استعلامات قاعدة البيانات"""
    try:
        # تفعيل التخزين المؤقت للاستعلامات
        session.execute("PRAGMA cache_size = 10000")
        
        # تحسين إعدادات الذاكرة
        session.execute("PRAGMA temp_store = MEMORY")
        
        # تحسين إعدادات المزامنة
        session.execute("PRAGMA synchronous = NORMAL")
        
        print("✅ تم تحسين إعدادات قاعدة البيانات")
        
    except Exception as e:
        print(f"خطأ في تحسين قاعدة البيانات: {str(e)}")

def create_performance_timer():
    """إنشاء مؤقت لتحسين الأداء الدوري"""
    timer = QTimer()
    timer.timeout.connect(lambda: PerformanceOptimizer.reduce_memory_usage())
    timer.start(300000)  # كل 5 دقائق
    return timer
