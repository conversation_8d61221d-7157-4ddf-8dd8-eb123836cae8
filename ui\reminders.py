from PyQt5.QtWidgets import (Q<PERSON><PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QMessageBox, QDialog, QTextEdit, QComboBox,
                            QDateTimeEdit, QFormLayout, QGroupBox, QCheckBox, QSizePolicy, QFrame,
                            QMenu, QAction, QFileDialog)
from PyQt5.QtCore import Qt, QDateTime, QDate, QTime, QTimer
from PyQt5.QtGui import QIcon, QFont, QColor, QPainter

from database import Reminder
from utils import show_error_message, show_info_message, show_confirmation_message, format_datetime
import datetime

from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, StyledLabel, StyledTabWidget)

class ReminderDialog(QDialog):
    """نافذة حوار لإضافة/تعديل تنبيه"""

    def __init__(self, parent=None, reminder=None, session=None):
        super().__init__(parent)
        self.reminder = reminder
        self.session = session
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("تنبيه جديد" if not self.reminder else "تعديل التنبيه")
        self.setMinimumWidth(400)

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout()

        # إنشاء نموذج الإدخال
        form_layout = QFormLayout()

        # عنوان التنبيه
        self.title_edit = QLineEdit()
        if self.reminder:
            self.title_edit.setText(self.reminder.title)
        form_layout.addRow("العنوان:", self.title_edit)

        # وصف التنبيه
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        if self.reminder and self.reminder.description:
            self.description_edit.setText(self.reminder.description)
        form_layout.addRow("الوصف:", self.description_edit)

        # تاريخ ووقت التنبيه
        self.date_time_edit = QDateTimeEdit()
        self.date_time_edit.setCalendarPopup(True)
        self.date_time_edit.setDateTime(QDateTime.currentDateTime())
        if self.reminder and self.reminder.reminder_date:
            self.date_time_edit.setDateTime(QDateTime(self.reminder.reminder_date))
        form_layout.addRow("تاريخ ووقت التنبيه:", self.date_time_edit)

        # الأولوية
        self.priority_combo = QComboBox()
        self.priority_combo.addItem("عالية", "high")
        self.priority_combo.addItem("متوسطة", "medium")
        self.priority_combo.addItem("منخفضة", "low")
        if self.reminder and self.reminder.priority:
            index = self.priority_combo.findData(self.reminder.priority)
            if index >= 0:
                self.priority_combo.setCurrentIndex(index)
        form_layout.addRow("الأولوية:", self.priority_combo)

        # حالة الإكمال
        self.completed_check = QCheckBox("مكتمل")
        if self.reminder and self.reminder.is_completed:
            self.completed_check.setChecked(True)
        form_layout.addRow("الحالة:", self.completed_check)

        # إضافة التخطيط إلى التخطيط الرئيسي
        layout.addLayout(form_layout)

        # إنشاء أزرار الإجراءات
        button_layout = QHBoxLayout()

        self.save_button = StyledButton("حفظ", "success", "normal")
        self.save_button.clicked.connect(self.save_reminder)

        self.cancel_button = StyledButton("إلغاء", "secondary", "normal")
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(self.save_button.button)
        button_layout.addWidget(self.cancel_button.button)

        layout.addLayout(button_layout)

        self.setLayout(layout)

    def save_reminder(self):
        """حفظ التنبيه"""
        title = self.title_edit.text().strip()
        if not title:
            show_error_message("خطأ", "يجب إدخال عنوان للتنبيه")
            return

        description = self.description_edit.toPlainText().strip()
        reminder_date = self.date_time_edit.dateTime().toPyDateTime()
        priority = self.priority_combo.currentData()
        is_completed = self.completed_check.isChecked()

        try:
            if not self.reminder:
                # إنشاء تنبيه جديد
                self.reminder = Reminder(
                    title=title,
                    description=description,
                    reminder_date=reminder_date,
                    priority=priority,
                    is_completed=is_completed,
                    user_id=None  # إزالة المستخدم المسؤول
                )
                self.session.add(self.reminder)
            else:
                # تحديث التنبيه الموجود
                self.reminder.title = title
                self.reminder.description = description
                self.reminder.reminder_date = reminder_date
                self.reminder.priority = priority
                self.reminder.is_completed = is_completed
                self.reminder.user_id = None  # إزالة المستخدم المسؤول

            self.session.commit()
            self.accept()
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء حفظ التنبيه: {str(e)}")

class RemindersWidget(QWidget):
    """واجهة إدارة التنبيهات"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        # تأجيل تحميل البيانات لتحسين الأداء
        QTimer.singleShot(600, self.refresh_data)

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مع تحسين العرض الكامل واستغلال أقصى مساحة للجدول
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # هوامش أصغر جداً لاستغلال المساحة القصوى
        main_layout.setSpacing(2)  # مسافات أصغر بين العناصر

        # تعيين سياسة الحجم للويدجت ليأخذ العرض الكامل
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("⏰ إدارة التنبيهات المتطورة - نظام شامل ومتقدم لإدارة التنبيهات مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن مع إطار أسود وارتفاع مناسب للتوسيط المثالي
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)
        # تعيين سياسة الحجم للإطار العلوي
        top_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        filter_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(filter_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالعنوان أو الوصف...")
        # سيتم ربط الأحداث في نهاية init_ui()
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QLineEdit:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
        """)
        # تعيين سياسة الحجم لحقل البحث ليأخذ المساحة المتاحة
        self.search_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                font-size: 20px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(91, 33, 182, 0.9),
                    stop:1 rgba(76, 29, 149, 0.8));
                border: 3px solid rgba(91, 33, 182, 0.9);
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
            }
        """)
        # سيتم ربط الأحداث في نهاية init_ui()
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية الأولوية محسنة مطابقة للفواتير
        priority_label = QLabel("⭐ أولوية:")
        priority_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 75px;
                max-width: 75px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        priority_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة للأولويات مطابقة للفواتير
        try:
            self.create_custom_priority_filter()
        except Exception as e:
            print(f"❌ خطأ في إنشاء قائمة تصفية الأولويات: {str(e)}")
            # إنشاء قائمة بسيطة كبديل
            self.priority_filter_frame = QLabel("جميع الأولويات")
            self.current_priority_value = None


        # تسمية الحالة محسنة مطابقة للفواتير
        status_label = QLabel("🎯 حالة:")
        status_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        status_label.setAlignment(Qt.AlignCenter)

        # إنشاء قائمة تصفية مخصصة ومطورة للحالات مطابقة للفواتير
        try:
            self.create_custom_status_filter()
        except Exception as e:
            print(f"❌ خطأ في إنشاء قائمة تصفية الحالات: {str(e)}")
            # إنشاء قائمة بسيطة كبديل
            self.status_filter_frame = QLabel("جميع الحالات")
            self.current_status_value = None


        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        filter_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        filter_layout.addWidget(self.search_edit, 10, Qt.AlignVCenter)  # مساحة كبيرة جداً للبحث
        filter_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        filter_layout.addWidget(priority_label, 0, Qt.AlignVCenter)
        filter_layout.addWidget(self.priority_filter_frame, 1, Qt.AlignVCenter)
        filter_layout.addWidget(status_label, 0, Qt.AlignVCenter)
        filter_layout.addWidget(self.status_filter_frame, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول التنبيهات المتطور والمحسن
        self.create_advanced_reminders_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.reminders_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار مع إطار أسود
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e1);
                border: 3px solid #000000;
                border-radius: 8px;
                margin: 1px;
                padding: 2px;
                max-height: 70px;
                min-height: 65px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة تنبيه")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_reminder)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')  # أزرق سماوي متطور مطابق للفواتير
        self.edit_button.clicked.connect(self.edit_reminder)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_reminder)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.mark_completed_button = QPushButton("✅ تعليم كمكتمل")
        self.style_advanced_button(self.mark_completed_button, 'emerald')  # أخضر للإكمال
        self.mark_completed_button.clicked.connect(self.mark_as_completed)
        self.mark_completed_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'indigo', has_menu=True)  # بنفسجي للتفاصيل
        self.view_button.clicked.connect(self.view_reminder)
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)  # لون متسق مع نظام الألوان
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للتصدير مطابقة للأقسام الأخرى
        from ui.unified_styles import UnifiedStyles
        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style('info', 'normal'))

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        json_action = QAction("🔗 تصدير إلى JSON", self)
        json_action.triggered.connect(self.export_to_json)
        export_menu.addAction(json_action)

        self.export_button.setMenu(export_menu)



        # إجمالي التنبيهات مطور ليتشابه مع الفواتير
        self.total_label = QLabel("إجمالي التنبيهات: 0")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.mark_completed_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        main_layout.addWidget(bottom_frame)

        # ربط الأحداث في النهاية بعد إنشاء جميع العناصر
        self.connect_events()

        self.setLayout(main_layout)

    def connect_events(self):
        """ربط جميع الأحداث بعد إنشاء جميع العناصر"""
        try:
            # ربط أحداث البحث والتصفية
            self.search_edit.textChanged.connect(self.filter_reminders)
            print("✅ تم ربط أحداث التنبيهات بنجاح")
        except Exception as e:
            print(f"❌ خطأ في ربط أحداث التنبيهات: {str(e)}")

    def create_advanced_reminders_table(self):
        """إنشاء جدول التنبيهات المتطور والمحسن مطابق للموردين"""
        styled_table = StyledTable()
        self.reminders_table = styled_table.table
        self.reminders_table.setColumnCount(6)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🔢 ID",
            "⏰ العنوان",
            "📅 تاريخ التنبيه",
            "📝 تاريخ الإنشاء",
            "🎯 الأولوية",
            "📊 الحالة"
        ]
        self.reminders_table.setHorizontalHeaderLabels(headers)

        # إعدادات عرض الأعمدة مع التكيف التلقائي مطابقة للعملاء
        header = self.reminders_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # ID
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # العنوان
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # تاريخ التنبيه
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # تاريخ الإنشاء
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # الأولوية
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # الحالة

        # تحديد عرض الأعمدة الثابتة مطابق للعملاء
        self.reminders_table.setColumnWidth(0, 120)  # ID
        self.reminders_table.setColumnWidth(1, 300)  # العنوان

        self.reminders_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.reminders_table.setSelectionMode(QTableWidget.SingleSelection)
        self.reminders_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.reminders_table.setAlternatingRowColors(True)
        self.reminders_table.doubleClicked.connect(self.view_reminder)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.reminders_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.reminders_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)
                scrollbar.setPageStep(200)
        except Exception:
            pass

        # إضافة معالج التمرير المخصص
        def reminders_wheelEvent(event):
            try:
                delta = event.angleDelta().y()
                if abs(delta) < 120:
                    event.accept()
                    return

                scrollbar = self.reminders_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                if delta > 0:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()
            except Exception:
                QTableWidget.wheelEvent(self.reminders_table, event)

        self.reminders_table.wheelEvent = reminders_wheelEvent

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للمصروفات والإيرادات والفواتير
        self.reminders_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                /* color: #1e293b; */ /* تم إزالة اللون الثابت للسماح بألوان مخصصة */
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للمصروفات والإيرادات والفواتير
        header = self.reminders_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للمصروفات والإيرادات والفواتير
        self.reminders_table.verticalHeader().setDefaultSectionSize(45)
        self.reminders_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        # إضافة العلامة المائية للجدول مطابقة للفواتير
        self.add_watermark_to_reminders_table()

    def add_watermark_to_reminders_table(self):
        """إضافة علامة مائية للجدول مطابقة للعملاء"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")
            painter.restore()

        original_paint = self.reminders_table.paintEvent
        def new_paint_event(event):
            try:
                original_paint(event)
                painter = QPainter(self.reminders_table.viewport())
                paint_watermark(painter, self.reminders_table.viewport().rect())
                painter.end()
            except Exception:
                pass

        self.reminders_table.paintEvent = new_paint_event
        # إجبار إعادة الرسم
        self.reminders_table.viewport().update()
        self.reminders_table.repaint()

    def select_reminder(self, reminder_id):
        """تحديد تنبيه معين في الجدول"""
        # البحث عن التنبيه في الجدول
        for row in range(self.reminders_table.rowCount()):
            item = self.reminders_table.item(row, 0)  # عمود الرقم
            if item and int(item.text()) == reminder_id:
                # تحديد الصف
                self.reminders_table.selectRow(row)
                # التمرير إلى الصف المحدد
                self.reminders_table.scrollToItem(item)
                # عرض تفاصيل التنبيه
                self.view_reminder()
                return True

        # إذا لم يتم العثور على التنبيه، قم بتحديث البيانات وحاول مرة أخرى
        self.refresh_data()

        # محاولة أخرى بعد تحديث البيانات
        for row in range(self.reminders_table.rowCount()):
            item = self.reminders_table.item(row, 0)  # عمود الرقم
            if item and int(item.text()) == reminder_id:
                # تحديد الصف
                self.reminders_table.selectRow(row)
                # التمرير إلى الصف المحدد
                self.reminders_table.scrollToItem(item)
                # عرض تفاصيل التنبيه
                self.view_reminder()
                return True

        return False

    def refresh_data(self):
        """تحديث بيانات التنبيهات في الجدول"""
        # الحصول على جميع التنبيهات من قاعدة البيانات
        reminders = self.session.query(Reminder).order_by(Reminder.reminder_date).all()
        self.populate_table(reminders)
        self.update_summary(reminders)

    def update_summary(self, reminders):
        """تحديث ملخص التنبيهات"""
        total = len(reminders)
        completed = sum(1 for reminder in reminders if reminder.is_completed)
        pending = total - completed

        self.total_label.setText(f"إجمالي التنبيهات: {total} | مكتمل: {completed} | معلق: {pending}")

    def populate_table(self, reminders):
        """ملء جدول التنبيهات بالبيانات"""
        self.reminders_table.setRowCount(0)

        # دالة مساعدة لإنشاء العناصر مطابق للعملاء
        def create_item(icon, text, default="No Data"):
            display_text = text if text and text.strip() else default
            item = QTableWidgetItem(f"{icon} {display_text}")
            item.setTextAlignment(Qt.AlignCenter)
            if display_text == default:
                item.setForeground(QColor("#ef4444"))
            return item

        for row, reminder in enumerate(reminders):
            self.reminders_table.insertRow(row)

            # الرقم مع أيقونة ثابتة - لون أسود للأرقام مطابق للعملاء
            id_item = QTableWidgetItem(f"🔢 {reminder.id}")
            id_item.setTextAlignment(Qt.AlignCenter)
            id_item.setForeground(QColor("#000000"))  # لون أسود للرقم مطابق للعملاء
            self.reminders_table.setItem(row, 0, id_item)

            self.reminders_table.setItem(row, 1, create_item("⏰", reminder.title))

            # التواريخ مطابق للعملاء
            reminder_date_str = format_datetime(reminder.reminder_date) if reminder.reminder_date else None
            created_date_str = format_datetime(reminder.created_date) if reminder.created_date else None

            self.reminders_table.setItem(row, 2, create_item("📅", reminder_date_str))
            self.reminders_table.setItem(row, 3, create_item("📝", created_date_str))

            # الأولوية مطابق للعملاء
            priority_map = {
                'high': '🔴 عالية',
                'medium': '🟡 متوسطة',
                'low': '🟢 منخفضة'
            }
            priority_text = priority_map.get(reminder.priority, '🟡 غير محدد')
            priority_item = QTableWidgetItem(priority_text)
            priority_item.setTextAlignment(Qt.AlignCenter)
            self.reminders_table.setItem(row, 4, priority_item)

            # الحالة مطابق للعملاء
            if reminder.is_completed:
                status_text = "🟢 مكتمل"
            else:
                import datetime
                if reminder.reminder_date and reminder.reminder_date < datetime.datetime.now():
                    status_text = "🔴 متأخر"
                elif reminder.reminder_date and reminder.reminder_date.date() == datetime.datetime.now().date():
                    status_text = "🟡 اليوم"
                else:
                    status_text = "🟡 معلق"

            status_item = QTableWidgetItem(status_text)
            status_item.setTextAlignment(Qt.AlignCenter)
            self.reminders_table.setItem(row, 5, status_item)

    def filter_reminders(self):
        """تصفية التنبيهات بناءً على معايير البحث"""
        search_text = self.search_edit.text().strip().lower()
        priority = getattr(self, 'current_priority_value', None)
        status = getattr(self, 'current_status_value', None)

        # بناء الاستعلام
        query = self.session.query(Reminder)

        # إضافة شروط البحث
        if search_text:
            query = query.filter(Reminder.title.like(f"%{search_text}%") |
                                Reminder.description.like(f"%{search_text}%"))

        if priority is not None:
            query = query.filter(Reminder.priority == priority)

        if status is not None:
            query = query.filter(Reminder.is_completed == status)

        # تنفيذ الاستعلام
        reminders = query.order_by(Reminder.reminder_date).all()

        # تحديث الجدول والملخص
        self.populate_table(reminders)
        self.update_summary(reminders)

    def add_reminder(self):
        """إضافة تنبيه جديد"""
        dialog = ReminderDialog(self, None, self.session)
        if dialog.exec_() == QDialog.Accepted:
            show_info_message("تم", "تمت إضافة التنبيه بنجاح")
            self.refresh_data()

    def edit_reminder(self):
        """تعديل تنبيه"""
        selected_row = self.reminders_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار تنبيه من القائمة")
            return

        reminder_id = int(self.reminders_table.item(selected_row, 0).text())
        reminder = self.session.query(Reminder).get(reminder_id)

        if not reminder:
            show_error_message("خطأ", "لم يتم العثور على التنبيه")
            return

        dialog = ReminderDialog(self, reminder, self.session)
        if dialog.exec_() == QDialog.Accepted:
            show_info_message("تم", "تم تحديث التنبيه بنجاح")
            self.refresh_data()

    def delete_reminder(self):
        """حذف تنبيه"""
        selected_row = self.reminders_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار تنبيه من القائمة")
            return

        reminder_id = int(self.reminders_table.item(selected_row, 0).text())
        reminder = self.session.query(Reminder).get(reminder_id)

        if not reminder:
            show_error_message("خطأ", "لم يتم العثور على التنبيه")
            return

        # طلب تأكيد الحذف
        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف التنبيه '{reminder.title}'؟"):
            self.session.delete(reminder)
            self.session.commit()
            show_info_message("تم", "تم حذف التنبيه بنجاح")
            self.refresh_data()

    def mark_as_completed(self):
        """تعليم التنبيه كمكتمل"""
        selected_row = self.reminders_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار تنبيه من القائمة")
            return

        reminder_id = int(self.reminders_table.item(selected_row, 0).text())
        reminder = self.session.query(Reminder).get(reminder_id)

        if not reminder:
            show_error_message("خطأ", "لم يتم العثور على التنبيه")
            return

        if reminder.is_completed:
            show_info_message("معلومات", "التنبيه مكتمل بالفعل")
            return

        reminder.is_completed = True
        self.session.commit()
        show_info_message("تم", "تم تعليم التنبيه كمكتمل")
        self.refresh_data()

    def view_reminder(self):
        """عرض تفاصيل التنبيه"""
        selected_row = self.reminders_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار تنبيه من القائمة")
            return

        reminder_id = int(self.reminders_table.item(selected_row, 0).text())
        reminder = self.session.query(Reminder).get(reminder_id)

        if not reminder:
            show_error_message("خطأ", "لم يتم العثور على التنبيه")
            return

        # إنشاء نافذة حوار لعرض التفاصيل
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تفاصيل التنبيه: {reminder.title}")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout()

        # عنوان التنبيه
        title_label = QLabel(f"<h2>{reminder.title}</h2>")
        layout.addWidget(title_label)

        # تاريخ التنبيه
        date_str = format_datetime(reminder.reminder_date) if reminder.reminder_date else ""
        date_label = QLabel(f"<b>تاريخ التنبيه:</b> {date_str}")
        layout.addWidget(date_label)

        # تاريخ الإنشاء
        created_date_str = format_datetime(reminder.created_date) if reminder.created_date else ""
        created_date_label = QLabel(f"<b>تاريخ الإنشاء:</b> {created_date_str}")
        layout.addWidget(created_date_label)

        # الأولوية
        priority_map = {
            'high': 'عالية',
            'medium': 'متوسطة',
            'low': 'منخفضة'
        }
        priority_text = priority_map.get(reminder.priority, reminder.priority or "")
        priority_label = QLabel(f"<b>الأولوية:</b> {priority_text}")
        layout.addWidget(priority_label)

        # الحالة
        status_text = "مكتمل" if reminder.is_completed else "غير مكتمل"
        status_label = QLabel(f"<b>الحالة:</b> {status_text}")
        layout.addWidget(status_label)

        # الوصف
        if reminder.description:
            description_label = QLabel("<b>الوصف:</b>")
            layout.addWidget(description_label)

            description_text = QTextEdit()
            description_text.setReadOnly(True)
            description_text.setText(reminder.description)
            description_text.setMaximumHeight(100)
            layout.addWidget(description_text)

        # زر إغلاق
        close_button = StyledButton("إغلاق", "secondary", "normal")
        close_button.clicked.connect(dialog.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button.button)

        layout.addLayout(button_layout)
        dialog.setLayout(layout)

        dialog.exec_()

    def export_to_excel(self):
        """تصدير التنبيهات إلى Excel"""
        try:
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel", "التنبيهات.csv", "CSV Files (*.csv)"
            )

            if file_path:
                reminders = self.session.query(Reminder).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    writer.writerow(['الرقم', 'العنوان', 'الوصف', 'التاريخ والوقت', 'الحالة', 'الأولوية'])

                    # كتابة البيانات
                    for reminder in reminders:
                        datetime_str = reminder.reminder_datetime.strftime("%Y-%m-%d %H:%M") if reminder.reminder_datetime else ""
                        status_text = "مكتمل" if reminder.is_completed else "نشط"
                        priority_text = reminder.priority or "عادي"

                        writer.writerow([
                            reminder.id,
                            reminder.title,
                            reminder.description or "",
                            datetime_str,
                            status_text,
                            priority_text
                        ])

                show_info_message("تم", f"تم تصدير التنبيهات بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def export_to_pdf(self):
        """تصدير التنبيهات إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument

            reminders = self.session.query(Reminder).all()

            if not reminders:
                show_info_message("تصدير PDF", "لا توجد تنبيهات للتصدير")
                return

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير التنبيهات", "تقرير_التنبيهات.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء محتوى HTML
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <title>تقرير التنبيهات</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        h1 {{ color: #f39c12; text-align: center; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                        th {{ background-color: #f2f2f2; }}
                        .completed {{ background-color: #d4edda; }}
                        .active {{ background-color: #fff3cd; }}
                    </style>
                </head>
                <body>
                    <h1>📅 تقرير التنبيهات</h1>
                    <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>
                    <p><strong>وقت الإنشاء:</strong> {QTime.currentTime().toString('hh:mm:ss')}</p>

                    <table>
                        <tr>
                            <th>الرقم</th>
                            <th>العنوان</th>
                            <th>الوصف</th>
                            <th>التاريخ والوقت</th>
                            <th>الحالة</th>
                            <th>الأولوية</th>
                        </tr>
                """

                for reminder in reminders:
                    datetime_str = reminder.reminder_datetime.strftime("%Y-%m-%d %H:%M") if reminder.reminder_datetime else ""
                    status_text = "مكتمل" if reminder.is_completed else "نشط"
                    status_class = "completed" if reminder.is_completed else "active"
                    priority_text = reminder.priority or "عادي"

                    html_content += f"""
                        <tr class="{status_class}">
                            <td>{reminder.id}</td>
                            <td>{reminder.title}</td>
                            <td>{reminder.description or ""}</td>
                            <td>{datetime_str}</td>
                            <td>{status_text}</td>
                            <td>{priority_text}</td>
                        </tr>
                    """

                html_content += """
                    </table>
                </body>
                </html>
                """

                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                show_info_message("تم", f"تم تصدير التنبيهات إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير PDF: {str(e)}")

    def export_to_csv(self):
        """تصدير التنبيهات إلى CSV"""
        self.export_to_excel()  # نفس الوظيفة

    def export_to_json(self):
        """تصدير التنبيهات إلى JSON"""
        try:
            import json

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف JSON", "التنبيهات.json", "JSON Files (*.json)"
            )

            if file_path:
                reminders = self.session.query(Reminder).all()

                reminders_data = []
                for reminder in reminders:
                    reminder_data = {
                        'id': reminder.id,
                        'title': reminder.title,
                        'description': reminder.description or "",
                        'reminder_datetime': reminder.reminder_datetime.strftime("%Y-%m-%d %H:%M:%S") if reminder.reminder_datetime else "",
                        'is_completed': reminder.is_completed,
                        'priority': reminder.priority or "عادي",
                        'created_at': reminder.created_at.strftime("%Y-%m-%d %H:%M:%S") if hasattr(reminder, 'created_at') and reminder.created_at else ""
                    }
                    reminders_data.append(reminder_data)

                export_data = {
                    "export_info": {
                        "export_date": QDate.currentDate().toString('yyyy-MM-dd'),
                        "export_time": QTime.currentTime().toString('hh:mm:ss'),
                        "total_reminders": len(reminders_data),
                        "completed_reminders": len([r for r in reminders if r.is_completed]),
                        "active_reminders": len([r for r in reminders if not r.is_completed]),
                        "exported_by": "نظام إدارة التنبيهات"
                    },
                    "reminders": reminders_data
                }

                with open(file_path, 'w', encoding='utf-8') as jsonfile:
                    json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)

                show_info_message("تم", f"تم تصدير التنبيهات بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}")



    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة مطابق للفواتير"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#047857', 'hover_mid': '#059669', 'hover_end': '#10b981', 'hover_bottom': '#34d399',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#052e16', 'pressed_border': '#064e3b',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#991b1b', 'hover_mid': '#dc2626', 'hover_end': '#ef4444', 'hover_bottom': '#f87171',
                    'hover_border': '#ef4444', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0284c7', 'bg_bottom': '#0ea5e9',
                    'hover_start': '#075985', 'hover_mid': '#0891b2', 'hover_end': '#0ea5e9', 'hover_bottom': '#38bdf8',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0284c7', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.5)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#14b8a6',
                    'hover_start': '#134e4a', 'hover_mid': '#0d9488', 'hover_end': '#14b8a6', 'hover_bottom': '#2dd4bf',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#06b6d4',
                    'hover_start': '#164e63', 'hover_mid': '#0891b2', 'hover_end': '#06b6d4', 'hover_bottom': '#22d3ee',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.5)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#ec4899',
                    'hover_start': '#831843', 'hover_mid': '#be185d', 'hover_end': '#ec4899', 'hover_bottom': '#f472b6',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.5)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#6366f1',
                    'hover_start': '#312e81', 'hover_mid': '#4f46e5', 'hover_end': '#6366f1', 'hover_bottom': '#818cf8',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4f46e5', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.5)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#f97316',
                    'hover_start': '#7c2d12', 'hover_mid': '#c2410c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#f97316', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#f97316', 'text': '#ffffff', 'shadow': 'rgba(249, 115, 22, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات - مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']},
                               0 0 30px rgba(255, 255, 255, 0.1);
                    letter-spacing: 0.3px;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 4px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px {color_scheme['shadow']},
                               0 0 40px rgba(255, 255, 255, 0.15);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_scheme['shadow']},
                               inset 0 1px 0 rgba(255, 255, 255, 0.2),
                               inset 0 -1px 0 rgba(0, 0, 0, 0.5),
                               0 0 15px {color_scheme['shadow']};
                }}
                QPushButton::menu-indicator {{
                    {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"}
                }}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر المتطور: {str(e)}")

    def create_custom_priority_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة للأولويات مطابقة للفواتير"""
        try:
            # إنشاء إطار للقائمة المخصصة
            self.priority_filter_frame = QFrame()
            self.priority_filter_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.2 rgba(248, 250, 252, 0.95),
                        stop:0.4 rgba(241, 245, 249, 0.9),
                        stop:0.6 rgba(248, 250, 252, 0.95),
                        stop:0.8 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(226, 232, 240, 0.85));
                    border: 3px solid rgba(96, 165, 250, 0.8);
                    border-radius: 15px;
                    padding: 6px 15px;
                    min-width: 500px;
                    max-width: 500px;
                    min-height: 33px;
                    max-height: 37px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
                QFrame:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(250, 251, 255, 0.95),
                        stop:0.2 rgba(241, 245, 249, 0.9),
                        stop:0.4 rgba(226, 232, 240, 0.85),
                        stop:0.6 rgba(241, 245, 249, 0.9),
                        stop:0.8 rgba(250, 251, 255, 0.95),
                        stop:1 rgba(255, 255, 255, 0.9));
                    border: 4px solid rgba(96, 165, 250, 0.9);
                    box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
                }
            """)

            # إنشاء تخطيط أفقي للإطار
            filter_layout = QHBoxLayout(self.priority_filter_frame)
            filter_layout.setContentsMargins(5, 0, 5, 0)
            filter_layout.setSpacing(8)

            # سهم يسار
            self.priority_left_arrow = QPushButton("▼")
            self.priority_left_arrow.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7));
                    border: 2px solid rgba(96, 165, 250, 0.6);
                    border-radius: 12px;
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 35px;
                    max-width: 35px;
                    min-height: 28px;
                    max-height: 28px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.9),
                        stop:0.2 rgba(96, 165, 250, 0.8),
                        stop:0.4 rgba(139, 92, 246, 0.7),
                        stop:0.6 rgba(124, 58, 237, 0.8),
                        stop:0.8 rgba(109, 40, 217, 0.9),
                        stop:1 rgba(91, 33, 182, 0.8));
                    border: 3px solid rgba(139, 92, 246, 0.9);
                }
            """)

            # النص الحالي
            self.current_priority_label = QLabel("جميع الأولويات")
            self.current_priority_label.setAlignment(Qt.AlignCenter)
            self.current_priority_label.setStyleSheet("""
                QLabel {
                    color: #1f2937;
                    font-size: 16px;
                    font-weight: 900;
                    background: transparent;
                    border: none;
                    padding: 0px 12px;
                    text-align: center;
                    max-width: 435px;
                    min-width: 435px;
                    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                    cursor: pointer;
                }
            """)

            # زر القائمة (سهم يمين)
            self.priority_menu_button = QPushButton("▼")
            self.priority_menu_button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7));
                    border: 2px solid rgba(96, 165, 250, 0.6);
                    border-radius: 12px;
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 35px;
                    max-width: 35px;
                    min-height: 28px;
                    max-height: 28px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.9),
                        stop:0.2 rgba(96, 165, 250, 0.8),
                        stop:0.4 rgba(139, 92, 246, 0.7),
                        stop:0.6 rgba(124, 58, 237, 0.8),
                        stop:0.8 rgba(109, 40, 217, 0.9),
                        stop:1 rgba(91, 33, 182, 0.8));
                    border: 3px solid rgba(139, 92, 246, 0.9);
                }
            """)

            # إضافة العناصر للتخطيط
            filter_layout.addWidget(self.priority_left_arrow, 0)
            filter_layout.addWidget(self.current_priority_label, 1)
            filter_layout.addWidget(self.priority_menu_button, 0)

            # إنشاء القائمة المنسدلة للأولويات
            self.priority_menu = QMenu(self)
            self.priority_menu.setStyleSheet("""
                QMenu {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.2 rgba(248, 250, 252, 0.95),
                        stop:0.4 rgba(241, 245, 249, 0.9),
                        stop:0.6 rgba(248, 250, 252, 0.95),
                        stop:0.8 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(226, 232, 240, 0.85));
                    border: 3px solid rgba(96, 165, 250, 0.8);
                    border-radius: 15px;
                    padding: 8px;
                    color: #1f2937;
                    font-weight: 900;
                    font-size: 17px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    min-width: 515px;
                    max-width: 515px;
                }
                QMenu::item {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.2 rgba(248, 250, 252, 0.95),
                        stop:0.4 rgba(241, 245, 249, 0.9),
                        stop:0.6 rgba(248, 250, 252, 0.95),
                        stop:0.8 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(226, 232, 240, 0.85));
                    border: 3px solid rgba(96, 165, 250, 0.8);
                    border-radius: 15px;
                    padding: 8px;
                    margin: 2px 5px;
                    min-height: 32px;
                    max-height: 32px;
                    max-width: 495px;
                    min-width: 495px;
                    color: #1f2937;
                    font-weight: 900;
                    font-size: 17px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    text-align: center;
                    font-smooth: never;
                    -webkit-font-smoothing: none;
                    -moz-osx-font-smoothing: auto;
                    text-rendering: optimizeSpeed;
                }
                QMenu::item:selected {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(96, 165, 250, 0.4),
                        stop:0.2 rgba(139, 92, 246, 0.3),
                        stop:0.4 rgba(124, 58, 237, 0.25),
                        stop:0.6 rgba(139, 92, 246, 0.3),
                        stop:0.8 rgba(96, 165, 250, 0.4),
                        stop:1 rgba(59, 130, 246, 0.35));
                    border: 3px solid rgba(96, 165, 250, 0.7);
                    color: #1f2937;
                    font-weight: 900;
                    font-size: 17px;
                    font-family: 'Courier New', 'Consolas', monospace;
                    font-smooth: never;
                    -webkit-font-smoothing: none;
                    -moz-osx-font-smoothing: auto;
                    text-rendering: optimizeSpeed;
                    box-shadow:
                        0 4px 12px rgba(96, 165, 250, 0.3),
                        0 0 15px rgba(96, 165, 250, 0.2),
                        inset 0 1px 2px rgba(255, 255, 255, 0.5);
                    transform: scale(1.02);
                }
                QMenu::item:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(250, 251, 255, 0.95),
                        stop:0.2 rgba(241, 245, 249, 0.9),
                        stop:0.4 rgba(226, 232, 240, 0.85),
                        stop:0.6 rgba(241, 245, 249, 0.9),
                        stop:0.8 rgba(250, 251, 255, 0.95),
                        stop:1 rgba(255, 255, 255, 0.9));
                    border: 4px solid rgba(96, 165, 250, 0.9);
                    color: #1f2937;
                    font-weight: 900;
                    font-size: 17px;
                    font-family: 'Courier New', 'Consolas', monospace;
                    font-smooth: never;
                    -webkit-font-smoothing: none;
                    -moz-osx-font-smoothing: auto;
                    text-rendering: optimizeSpeed;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                    transform: translateY(-1px);
                }
            """)

            # إضافة خيارات التصفية للأولويات مع أيقونات مطابقة للعملاء
            priority_options = [
                ("جميع الأولويات", None),
                ("🔴 عالية", "high"),
                ("🟡 متوسطة", "medium"),
                ("🟢 منخفضة", "low")
            ]

            for text, value in priority_options:
                # إنشاء عنصر مع توسيط النص المثالي
                centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
                action = QAction(centered_text, self)
                action.setData(value)
                action.triggered.connect(lambda checked, v=value, t=text: self.set_priority_filter(v, t))
                self.priority_menu.addAction(action)

            # ربط الأزرار بالقائمة
            self.priority_menu_button.clicked.connect(self.show_priority_menu)
            self.priority_left_arrow.clicked.connect(self.show_priority_menu)

            # إضافة ميزة الضغط على أي مكان في الإطار
            self.priority_filter_frame.mousePressEvent = self.priority_frame_mouse_press_event
            self.current_priority_label.mousePressEvent = self.priority_frame_mouse_press_event

            # تعيين القيم الافتراضية
            self.current_priority_value = None
            print("✅ تم إنشاء قائمة التصفية المخصصة للأولويات بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء قائمة التصفية المخصصة للأولويات: {str(e)}")
            import traceback
            traceback.print_exc()
            # إنشاء قائمة بسيطة كبديل
            self.priority_filter_frame = QLabel("جميع الأولويات")
            self.current_priority_value = None

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة للحالات مطابقة للفواتير"""
        try:
            # إنشاء إطار للقائمة المخصصة
            self.status_filter_frame = QFrame()
            self.status_filter_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.2 rgba(248, 250, 252, 0.95),
                        stop:0.4 rgba(241, 245, 249, 0.9),
                        stop:0.6 rgba(248, 250, 252, 0.95),
                        stop:0.8 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(226, 232, 240, 0.85));
                    border: 3px solid rgba(96, 165, 250, 0.8);
                    border-radius: 15px;
                    padding: 6px 15px;
                    min-width: 500px;
                    max-width: 500px;
                    min-height: 33px;
                    max-height: 37px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
                QFrame:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(250, 251, 255, 0.95),
                        stop:0.2 rgba(241, 245, 249, 0.9),
                        stop:0.4 rgba(226, 232, 240, 0.85),
                        stop:0.6 rgba(241, 245, 249, 0.9),
                        stop:0.8 rgba(250, 251, 255, 0.95),
                        stop:1 rgba(255, 255, 255, 0.9));
                    border: 4px solid rgba(96, 165, 250, 0.9);
                    box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
                }
            """)

            # إنشاء تخطيط أفقي للإطار
            filter_layout = QHBoxLayout(self.status_filter_frame)
            filter_layout.setContentsMargins(5, 0, 5, 0)
            filter_layout.setSpacing(8)

            # سهم يسار
            self.status_left_arrow = QPushButton("▼")
            self.status_left_arrow.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7));
                    border: 2px solid rgba(96, 165, 250, 0.6);
                    border-radius: 12px;
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 35px;
                    max-width: 35px;
                    min-height: 28px;
                    max-height: 28px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.9),
                        stop:0.2 rgba(96, 165, 250, 0.8),
                        stop:0.4 rgba(139, 92, 246, 0.7),
                        stop:0.6 rgba(124, 58, 237, 0.8),
                        stop:0.8 rgba(109, 40, 217, 0.9),
                        stop:1 rgba(91, 33, 182, 0.8));
                    border: 3px solid rgba(139, 92, 246, 0.9);
                }
            """)

            # النص الحالي
            self.current_status_label = QLabel("جميع الحالات")
            self.current_status_label.setAlignment(Qt.AlignCenter)
            self.current_status_label.setStyleSheet("""
                QLabel {
                    color: #1f2937;
                    font-size: 16px;
                    font-weight: 900;
                    background: transparent;
                    border: none;
                    padding: 0px 12px;
                    text-align: center;
                    max-width: 435px;
                    min-width: 435px;
                    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                    cursor: pointer;
                }
            """)

            # زر القائمة (سهم يمين)
            self.status_menu_button = QPushButton("▼")
            self.status_menu_button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7));
                    border: 2px solid rgba(96, 165, 250, 0.6);
                    border-radius: 12px;
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 35px;
                    max-width: 35px;
                    min-height: 28px;
                    max-height: 28px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.9),
                        stop:0.2 rgba(96, 165, 250, 0.8),
                        stop:0.4 rgba(139, 92, 246, 0.7),
                        stop:0.6 rgba(124, 58, 237, 0.8),
                        stop:0.8 rgba(109, 40, 217, 0.9),
                        stop:1 rgba(91, 33, 182, 0.8));
                    border: 3px solid rgba(139, 92, 246, 0.9);
                }
            """)

            # إضافة العناصر للتخطيط
            filter_layout.addWidget(self.status_left_arrow, 0)
            filter_layout.addWidget(self.current_status_label, 1)
            filter_layout.addWidget(self.status_menu_button, 0)

            # إنشاء القائمة المنسدلة للحالات
            self.status_menu = QMenu(self)
            self.status_menu.setStyleSheet("""
                QMenu {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.2 rgba(248, 250, 252, 0.95),
                        stop:0.4 rgba(241, 245, 249, 0.9),
                        stop:0.6 rgba(248, 250, 252, 0.95),
                        stop:0.8 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(226, 232, 240, 0.85));
                    border: 3px solid rgba(96, 165, 250, 0.8);
                    border-radius: 4px;
                    padding: 8px;
                    color: #1f2937;
                    font-weight: 900;
                    font-size: 17px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    min-width: 515px;
                    max-width: 515px;
                }
                QMenu::item {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.2 rgba(248, 250, 252, 0.95),
                        stop:0.4 rgba(241, 245, 249, 0.9),
                        stop:0.6 rgba(248, 250, 252, 0.95),
                        stop:0.8 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(226, 232, 240, 0.85));
                    border: 3px solid rgba(96, 165, 250, 0.8);
                    border-radius: 15px;
                    padding: 8px;
                    margin: 2px 5px;
                    min-height: 32px;
                    max-height: 32px;
                    max-width: 495px;
                    min-width: 495px;
                    color: #1f2937;
                    font-weight: 900;
                    font-size: 17px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    text-align: center;
                    font-smooth: never;
                    -webkit-font-smoothing: none;
                    -moz-osx-font-smoothing: auto;
                    text-rendering: optimizeSpeed;
                }
                QMenu::item:selected {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(96, 165, 250, 0.4),
                        stop:0.2 rgba(139, 92, 246, 0.3),
                        stop:0.4 rgba(124, 58, 237, 0.25),
                        stop:0.6 rgba(139, 92, 246, 0.3),
                        stop:0.8 rgba(96, 165, 250, 0.4),
                        stop:1 rgba(59, 130, 246, 0.35));
                    border: 3px solid rgba(96, 165, 250, 0.7);
                    color: #1f2937;
                    font-weight: 900;
                    font-size: 17px;
                    font-family: 'Courier New', 'Consolas', monospace;
                    font-smooth: never;
                    -webkit-font-smoothing: none;
                    -moz-osx-font-smoothing: auto;
                    text-rendering: optimizeSpeed;
                    box-shadow:
                        0 4px 12px rgba(96, 165, 250, 0.3),
                        0 0 15px rgba(96, 165, 250, 0.2),
                        inset 0 1px 2px rgba(255, 255, 255, 0.5);
                    transform: scale(1.02);
                }
                QMenu::item:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(250, 251, 255, 0.95),
                        stop:0.2 rgba(241, 245, 249, 0.9),
                        stop:0.4 rgba(226, 232, 240, 0.85),
                        stop:0.6 rgba(241, 245, 249, 0.9),
                        stop:0.8 rgba(250, 251, 255, 0.95),
                        stop:1 rgba(255, 255, 255, 0.9));
                    border: 4px solid rgba(96, 165, 250, 0.9);
                    color: #1f2937;
                    font-weight: 900;
                    font-size: 17px;
                    font-family: 'Courier New', 'Consolas', monospace;
                    font-smooth: never;
                    -webkit-font-smoothing: none;
                    -moz-osx-font-smoothing: auto;
                    text-rendering: optimizeSpeed;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                    transform: translateY(-1px);
                }
            """)

            # إضافة خيارات التصفية للحالات مع أيقونات مطابقة للعملاء
            status_options = [
                ("جميع الحالات", None),
                ("🟢 مكتمل", True),
                ("🔴 غير مكتمل", False)
            ]

            for text, value in status_options:
                # إنشاء عنصر مع توسيط النص المثالي
                centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
                action = QAction(centered_text, self)
                action.setData(value)
                action.triggered.connect(lambda checked, v=value, t=text: self.set_status_filter(v, t))
                self.status_menu.addAction(action)

            # ربط الأزرار بالقائمة
            self.status_menu_button.clicked.connect(self.show_status_menu)
            self.status_left_arrow.clicked.connect(self.show_status_menu)

            # إضافة ميزة الضغط على أي مكان في الإطار
            self.status_filter_frame.mousePressEvent = self.status_frame_mouse_press_event
            self.current_status_label.mousePressEvent = self.status_frame_mouse_press_event

            # تعيين القيم الافتراضية
            self.current_status_value = None
            print("✅ تم إنشاء قائمة التصفية المخصصة للحالات بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء قائمة التصفية المخصصة للحالات: {str(e)}")
            import traceback
            traceback.print_exc()
            # إنشاء قائمة بسيطة كبديل
            self.status_filter_frame = QLabel("جميع الحالات")
            self.current_status_value = None

    # دوال مساعدة للقوائم المخصصة
    def show_priority_menu(self):
        """عرض قائمة تصفية الأولويات"""
        try:
            button = self.sender()
            if button:
                self.priority_menu.exec_(button.mapToGlobal(button.rect().bottomLeft()))
            else:
                self.priority_menu.exec_(self.priority_filter_frame.mapToGlobal(self.priority_filter_frame.rect().bottomLeft()))
        except Exception as e:
            print(f"خطأ في عرض قائمة الأولويات: {str(e)}")

    def set_priority_filter(self, value, text):
        """تعيين تصفية الأولوية"""
        self.current_priority_value = value
        self.current_priority_label.setText(text)
        self.filter_reminders()

    def priority_frame_mouse_press_event(self, event):
        """التعامل مع الضغط على إطار تصفية الأولوية"""
        self.show_priority_menu()

    def show_status_menu(self):
        """عرض قائمة تصفية الحالات"""
        try:
            button = self.sender()
            if button:
                self.status_menu.exec_(button.mapToGlobal(button.rect().bottomLeft()))
            else:
                self.status_menu.exec_(self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft()))
        except Exception as e:
            print(f"خطأ في عرض قائمة الحالات: {str(e)}")

    def set_status_filter(self, value, text):
        """تعيين تصفية الحالة"""
        self.current_status_value = value
        self.current_status_label.setText(text)
        self.filter_reminders()

    def status_frame_mouse_press_event(self, event):
        """التعامل مع الضغط على إطار تصفية الحالة"""
        self.show_status_menu()

